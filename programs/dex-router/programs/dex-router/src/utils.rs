use crate::constants::Dex;

/// DEX 工具函数模块
/// 包含与 DEX 操作相关的通用工具函数

/// 获取 DEX 需要的账户数量
/// 
/// 返回指定 DEX 类型在执行交换操作时需要的账户数量。
/// 使用 u8 类型因为 DEX 账户数量通常不会超过 255 个。
/// 
/// # 参数
/// * `dex` - DEX 类型枚举
/// 
/// # 返回值
/// * `u8` - 该 DEX 需要的账户数量
/// 
/// # 示例
/// ```rust
/// use crate::utils::get_accounts_needed_for_dex;
/// use crate::constants::Dex;
/// 
/// let accounts_needed = get_accounts_needed_for_dex(Dex::RaydiumClmm);
/// assert_eq!(accounts_needed, 8);
/// ```
pub fn get_accounts_needed_for_dex(dex: Dex) -> u8 {
    match dex {
        Dex::RaydiumClmm | Dex::RaydiumCpmm => 8,
        Dex::MeteoraLb => 10,
        Dex::MeteoraAmm => 8,
        Dex::Orca => 9,
        Dex::PumpSwap => 8,
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_get_accounts_needed_for_dex() {
        // 测试各种 DEX 类型的账户数量
        assert_eq!(get_accounts_needed_for_dex(Dex::RaydiumClmm), 8);
        assert_eq!(get_accounts_needed_for_dex(Dex::RaydiumCpmm), 8);
        assert_eq!(get_accounts_needed_for_dex(Dex::MeteoraLb), 10);
        assert_eq!(get_accounts_needed_for_dex(Dex::MeteoraAmm), 8);
        assert_eq!(get_accounts_needed_for_dex(Dex::Orca), 9);
        assert_eq!(get_accounts_needed_for_dex(Dex::PumpSwap), 8);
    }

    #[test]
    fn test_return_type_size() {
        // 确保返回值类型是 u8
        let result = get_accounts_needed_for_dex(Dex::RaydiumClmm);
        assert_eq!(std::mem::size_of_val(&result), 1); // u8 占用 1 字节
    }

    #[test]
    fn test_all_dex_variants() {
        // 确保所有 DEX 变体都有对应的账户数量定义
        let dex_variants = [
            Dex::RaydiumClmm,
            Dex::RaydiumCpmm,
            Dex::MeteoraLb,
            Dex::MeteoraAmm,
            Dex::Orca,
            Dex::PumpSwap,
        ];

        for dex in dex_variants.iter() {
            let accounts_needed = get_accounts_needed_for_dex(*dex);
            // 所有 DEX 的账户数量都应该在合理范围内
            assert!(accounts_needed >= 5 && accounts_needed <= 20);
        }
    }
}
