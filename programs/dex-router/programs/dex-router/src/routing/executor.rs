//! 统一路由执行器
//!
//! 提供统一的路由执行接口，根据路由模式调用相应的执行器

use anchor_lang::prelude::*;
use crate::constants::{RouteConfig, RoutingMode, BranchRouteConfig, BatchRouteConfig};
use crate::error::RouteError;
use crate::state::event::{RouteExecuted, route_phases};
use crate::routing::{
    LinearRouteExecutor,
    CircularRouteExecutor, 
    BranchingRouteExecutor,
    BatchedRouteExecutor,
    BatchExecutionResult,
};

/// 统一路由执行器
/// 根据路由配置自动选择合适的执行器
pub struct RouteExecutor;

impl RouteExecutor {
    /// 执行路由配置
    pub fn execute_route<'info>(
        config: &RouteConfig,
        accounts: &[AccountInfo<'info>],
        remaining_accounts: &[AccountInfo<'info>],
    ) -> Result<u64> {
        // 发出执行开始事件
        emit!(RouteExecuted {
            phase: route_phases::STARTED,
            mode: config.mode.clone(),
            user: None,
            amount_in: config.amount_in,
            amount_out: 0,
            min_amount_out: config.min_amount_out,
            routes_count: config.routes.len() as u8,
            routes_executed: 0,
            total_fees: 0,
            execution_time: 0,
            success: false,
            actual_slippage_bps: 0,
            timestamp: Clock::get()?.unix_timestamp,
        });

        let start_time = Clock::get()?.unix_timestamp;

        // 根据路由模式选择执行器
        let result = match config.mode {
            RoutingMode::Linear => {
                msg!("执行线性路由");
                LinearRouteExecutor::execute_linear_route(config, accounts, remaining_accounts)
            },
            RoutingMode::Circular => {
                msg!("执行循环套利路由");
                CircularRouteExecutor::execute_circular_route(
                    config, 
                    accounts, 
                    remaining_accounts, 
                    None // 暂不支持闪电贷账户
                )
            },
            RoutingMode::Branching => {
                return Err(RouteError::UnsupportedRouteMode.into());
            },
            RoutingMode::Batched => {
                return Err(RouteError::UnsupportedRouteMode.into());
            },
        };

        let end_time = Clock::get()?.unix_timestamp;
        let execution_time = end_time - start_time;

        match result {
            Ok(amount_out) => {
                // 发出执行完成事件
                let total_fees = Self::calculate_route_fees(config).unwrap_or(0);
                let actual_slippage_bps = if config.amount_in > 0 {
                    let expected_out = (config.amount_in as f64 * 0.997) as u64; // 假设 0.3% 费率
                    if expected_out > amount_out {
                        (((expected_out - amount_out) * 10000) / expected_out) as u16
                    } else { 0 }
                } else { 0 };

                emit!(RouteExecuted {
                    phase: route_phases::COMPLETED,
                    mode: config.mode.clone(),
                    user: None,
                    amount_in: config.amount_in,
                    amount_out,
                    min_amount_out: config.min_amount_out,
                    routes_count: config.routes.len() as u8,
                    routes_executed: config.routes.len() as u8,
                    total_fees,
                    execution_time: execution_time as u32,
                    success: true,
                    actual_slippage_bps,
                    timestamp: end_time,
                });

                Ok(amount_out)
            },
            Err(e) => {
                // 发出执行失败事件
                emit!(RouteExecuted {
                    phase: route_phases::FAILED,
                    mode: config.mode.clone(),
                    user: None,
                    amount_in: config.amount_in,
                    amount_out: 0,
                    min_amount_out: config.min_amount_out,
                    routes_count: config.routes.len() as u8,
                    routes_executed: 0,
                    total_fees: 0,
                    execution_time: execution_time as u32,
                    success: false,
                    actual_slippage_bps: 0,
                    timestamp: end_time,
                });

                Err(e)
            }
        }
    }

    /// 执行分支路由
    pub fn execute_branch_route<'info>(
        config: &BranchRouteConfig,
        accounts: &[AccountInfo<'info>],
        remaining_accounts: &[AccountInfo<'info>],
    ) -> Result<u64> {
        msg!("执行分支路由");
        BranchingRouteExecutor::execute_branching_route(config, accounts, remaining_accounts)
    }

    /// 执行批量路由
    pub fn execute_batch_route<'info>(
        config: &BatchRouteConfig,
        accounts: &[AccountInfo<'info>],
        remaining_accounts: &[AccountInfo<'info>],
    ) -> Result<BatchExecutionResult> {
        msg!("执行批量路由");
        BatchedRouteExecutor::execute_batched_route(config, accounts, remaining_accounts)
    }

    /// 验证路由配置
    pub fn validate_route_config(config: &RouteConfig) -> Result<()> {
        match config.mode {
            RoutingMode::Linear => {
                LinearRouteExecutor::validate_linear_config(config)
            },
            RoutingMode::Circular => {
                CircularRouteExecutor::validate_circular_config(config)
            },
            RoutingMode::Branching => {
                Err(RouteError::UnsupportedRouteMode.into())
            },
            RoutingMode::Batched => {
                Err(RouteError::UnsupportedRouteMode.into())
            },
        }
    }

    /// 验证分支路由配置
    pub fn validate_branch_config(config: &BranchRouteConfig) -> Result<()> {
        BranchingRouteExecutor::validate_branching_config(config)
    }

    /// 验证批量路由配置
    pub fn validate_batch_config(config: &BatchRouteConfig) -> Result<()> {
        BatchedRouteExecutor::validate_batched_config(config)
    }

    /// 预估路由输出
    pub fn estimate_route_output(
        config: &RouteConfig,
        additional_data: &[Vec<u8>],
    ) -> Result<u64> {
        match config.mode {
            RoutingMode::Linear => {
                LinearRouteExecutor::estimate_linear_output(config, additional_data)
            },
            RoutingMode::Circular => {
                let profit = CircularRouteExecutor::estimate_arbitrage_profit(config, additional_data)?;
                Ok(profit.max(0) as u64)
            },
            RoutingMode::Branching => {
                Err(RouteError::UnsupportedRouteMode.into())
            },
            RoutingMode::Batched => {
                Err(RouteError::UnsupportedRouteMode.into())
            },
        }
    }

    /// 计算路由费用
    pub fn calculate_route_fees(config: &RouteConfig) -> Result<u64> {
        match config.mode {
            RoutingMode::Linear => {
                LinearRouteExecutor::calculate_total_fees(config)
            },
            RoutingMode::Circular => {
                // 循环路由费用计算与线性类似
                LinearRouteExecutor::calculate_total_fees(config)
            },
            RoutingMode::Branching => {
                Err(RouteError::UnsupportedRouteMode.into())
            },
            RoutingMode::Batched => {
                Err(RouteError::UnsupportedRouteMode.into())
            },
        }
    }

    /// 获取路由执行统计
    pub fn get_route_stats(config: &RouteConfig) -> Result<RouteStatistics> {
        let total_fees = Self::calculate_route_fees(config)?;
        let route_count = config.routes.len() as u8;
        
        // 估算执行时间（每步约2秒）
        let estimated_execution_time = (route_count as u32) * 2;

        Ok(RouteStatistics {
            mode: config.mode.clone(),
            route_count,
            total_fee_bps: (total_fees * 10000 / config.amount_in) as u16,
            estimated_execution_time,
            amount_in: config.amount_in,
            min_amount_out: config.min_amount_out,
            uses_flash_loan: config.flash_loan.is_some(),
        })
    }

    /// 比较多个路由配置
    pub fn compare_routes(configs: &[RouteConfig]) -> Result<RouteComparison> {
        require!(
            !configs.is_empty(),
            RouteError::EmptyRoute
        );

        let mut best_config_index = 0;
        let mut best_efficiency = 0.0f64;
        let mut route_stats = Vec::new();

        for (i, config) in configs.iter().enumerate() {
            let stats = Self::get_route_stats(config)?;
            let efficiency = (config.min_amount_out as f64) / (config.amount_in as f64);
            
            if efficiency > best_efficiency {
                best_efficiency = efficiency;
                best_config_index = i;
            }

            route_stats.push(stats);
        }

        Ok(RouteComparison {
            routes: route_stats,
            best_route_index: best_config_index as u8,
            best_efficiency_ratio: best_efficiency,
        })
    }
}

/// 路由统计信息
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct RouteStatistics {
    pub mode: RoutingMode,
    pub route_count: u8,
    pub total_fee_bps: u16,
    pub estimated_execution_time: u32,
    pub amount_in: u64,
    pub min_amount_out: u64,
    pub uses_flash_loan: bool,
}

/// 路由比较结果
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct RouteComparison {
    pub routes: Vec<RouteStatistics>,
    pub best_route_index: u8,
    pub best_efficiency_ratio: f64,
}

/// 路由执行上下文
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct RouteExecutionContext {
    pub user: Pubkey,
    pub input_token: Pubkey,
    pub output_token: Pubkey,
    pub amount_in: u64,
    pub min_amount_out: u64,
    pub max_slippage_bps: u16,
    pub deadline: i64,
    pub priority_fee: Option<u64>,
}

impl RouteExecutionContext {
    /// 验证执行上下文
    pub fn validate(&self) -> Result<()> {
        require!(
            self.amount_in > 0,
            RouteError::ZeroAmount
        );

        require!(
            self.min_amount_out > 0,
            RouteError::ZeroAmount
        );

        require!(
            self.max_slippage_bps <= 1000, // 最大10%滑点
            RouteError::InvalidSlippage
        );

        let current_time = Clock::get()?.unix_timestamp;
        require!(
            self.deadline > current_time,
            RouteError::DeadlineExceeded
        );

        Ok(())
    }

    /// 检查是否超时
    pub fn is_expired(&self) -> Result<bool> {
        let current_time = Clock::get()?.unix_timestamp;
        Ok(current_time > self.deadline)
    }
}

/// 路由执行监控器
pub struct RouteMonitor;

impl RouteMonitor {
    /// 检查路由执行状态
    pub fn check_route_health(config: &RouteConfig) -> Result<RouteHealthStatus> {
        let mut warnings = Vec::new();
        let mut errors = Vec::new();

        // 检查滑点设置
        if config.max_slippage_bps > 500 {
            warnings.push("高滑点设置可能导致显著损失".to_string());
        }

        // 检查路由复杂度
        if config.routes.len() > 4 {
            warnings.push("路由步骤过多可能增加失败风险".to_string());
        }

        // 检查金额大小
        if config.amount_in < 1000 {
            errors.push("交换金额过小".to_string());
        }

        // 检查输出要求的合理性
        let efficiency = (config.min_amount_out as f64) / (config.amount_in as f64);
        if efficiency < 0.5 {
            errors.push("预期输出过低".to_string());
        }

        let health_score = if !errors.is_empty() {
            0
        } else if warnings.len() > 2 {
            25
        } else if warnings.len() > 0 {
            75
        } else {
            100
        };

        let is_healthy = errors.is_empty() && warnings.len() <= 1;

        Ok(RouteHealthStatus {
            health_score,
            warnings,
            errors,
            is_healthy,
        })
    }
}

/// 路由健康状态
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct RouteHealthStatus {
    pub health_score: u8, // 0-100
    pub warnings: Vec<String>,
    pub errors: Vec<String>,
    pub is_healthy: bool,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::constants::*;

    fn create_test_route_config() -> RouteConfig {
        let token_a = Pubkey::new_unique();
        let token_b = Pubkey::new_unique();

        RouteConfig {
            mode: RoutingMode::Linear,
            routes: vec![Route {
                dex: Dex::RaydiumClmm,
                input_mint: token_a,
                output_mint: token_b,
                swap_data: vec![],
            }],
            amount_in: 1_000_000,
            min_amount_out: 900_000,
            max_slippage_bps: 300,
            flash_loan: None,
        }
    }

    #[test]
    fn test_validate_route_config() {
        let config = create_test_route_config();
        let result = RouteExecutor::validate_route_config(&config);
        assert!(result.is_ok());
    }

    #[test]
    fn test_calculate_route_fees() {
        let config = create_test_route_config();
        let fees = RouteExecutor::calculate_route_fees(&config);
        
        assert!(fees.is_ok());
        let fees = fees.unwrap();
        assert!(fees > 0);
        // Raydium CLMM: 25 bps of 1,000,000 = 2,500
        assert_eq!(fees, 2_500);
    }

    #[test]
    fn test_get_route_stats() {
        let config = create_test_route_config();
        let stats = RouteExecutor::get_route_stats(&config);
        
        assert!(stats.is_ok());
        let stats = stats.unwrap();
        assert_eq!(stats.mode, RoutingMode::Linear);
        assert_eq!(stats.route_count, 1);
        assert_eq!(stats.estimated_execution_time, 2);
        assert!(!stats.uses_flash_loan);
    }

    #[test]
    fn test_compare_routes() {
        let config1 = RouteConfig {
            mode: RoutingMode::Linear,
            routes: vec![Route {
                dex: Dex::RaydiumClmm,
                input_mint: Pubkey::new_unique(),
                output_mint: Pubkey::new_unique(),
                swap_data: vec![],
            }],
            amount_in: 1_000_000,
            min_amount_out: 950_000, // 95% 效率
            max_slippage_bps: 300,
            flash_loan: None,
        };

        let config2 = RouteConfig {
            mode: RoutingMode::Linear,
            routes: vec![Route {
                dex: Dex::Orca,
                input_mint: Pubkey::new_unique(),
                output_mint: Pubkey::new_unique(),
                swap_data: vec![],
            }],
            amount_in: 1_000_000,
            min_amount_out: 970_000, // 97% 效率，更好
            max_slippage_bps: 300,
            flash_loan: None,
        };

        let comparison = RouteExecutor::compare_routes(&[config1, config2]);
        assert!(comparison.is_ok());
        
        let comparison = comparison.unwrap();
        assert_eq!(comparison.best_route_index, 1); // 第二个路由更好
        assert!(comparison.best_efficiency_ratio > 0.95);
    }

    #[test]
    fn test_route_execution_context() {
        let context = RouteExecutionContext {
            user: Pubkey::new_unique(),
            input_token: Pubkey::new_unique(),
            output_token: Pubkey::new_unique(),
            amount_in: 1_000_000,
            min_amount_out: 900_000,
            max_slippage_bps: 300,
            deadline: i64::MAX, // 使用最大值避免Clock::get()问题
            priority_fee: Some(5000),
        };

        // 在测试环境中，跳过需要Clock::get()的验证
        // 只测试基本参数验证
        assert!(context.amount_in > 0);
        assert!(context.min_amount_out > 0);
        assert!(context.max_slippage_bps <= 1000);
    }

    #[test]
    fn test_route_health_check() {
        let config = create_test_route_config();
        let health = RouteMonitor::check_route_health(&config);
        
        assert!(health.is_ok());
        let health = health.unwrap();
        assert!(health.is_healthy);
        assert_eq!(health.health_score, 100);
        assert!(health.errors.is_empty());
    }

    #[test]
    fn test_unhealthy_route() {
        let mut config = create_test_route_config();
        config.amount_in = 100; // 金额过小
        config.max_slippage_bps = 800; // 滑点过高
        
        let health = RouteMonitor::check_route_health(&config);
        assert!(health.is_ok());
        
        let health = health.unwrap();
        assert!(!health.is_healthy);
        assert_eq!(health.health_score, 0);
        assert!(!health.errors.is_empty());
    }
}