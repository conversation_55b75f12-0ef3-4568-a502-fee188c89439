//! PumpSwap DEX 适配器
//!
//! 支持PumpSwap协议（简化的bonding curve交换）

use anchor_lang::prelude::*;
use anchor_spl::token::TokenAccount;
use anchor_lang::solana_program::{instruction::Instruction, instruction::AccountMeta};
use std::str::FromStr;
use crate::adapters::common::*;
use crate::constants::Dex;
use crate::error::RouteError;

/// PumpSwap 适配器
pub struct PumpSwapProcessor;

impl DexProcessor for PumpSwapProcessor {
    fn execute_swap_cpi<'info>(
        &self,
        accounts: &[AccountInfo<'info>],
        amount_in: u64,
        min_amount_out: u64,
        additional_args: &[u8],
    ) -> Result<u64> {
        // 验证账户数量
        require!(
            accounts.len() >= 8,
            RouteError::InvalidDexAccounts
        );

        // PumpSwap 账户布局
        let global = &accounts[0];
        let fee_recipient = &accounts[1];
        let mint = &accounts[2];
        let bonding_curve = &accounts[3];
        let associated_bonding_curve = &accounts[4];
        let associated_user = &accounts[5];
        let user = &accounts[6];
        let token_program = &accounts[7];

        // PumpSwap 程序ID
        let pumpswap_program_id = Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P")
            .map_err(|_| RouteError::SystemConfigError)?;

        // 构建交换指令数据
        let mut instruction_data = Vec::new();
        
        // PumpSwap 交换指令标识符
        let discriminator: [u8; 8] = [0x09, 0x0e, 0x6e, 0x9b, 0x8b, 0x15, 0x4a, 0xc8];
        instruction_data.extend_from_slice(&discriminator);
        
        // 添加交换参数
        instruction_data.extend_from_slice(&amount_in.to_le_bytes());
        instruction_data.extend_from_slice(&min_amount_out.to_le_bytes());
        
        // 添加交换方向（buy/sell）
        let is_buy = if additional_args.is_empty() { 
            true // 默认为买入
        } else {
            additional_args[0] != 0
        };
        instruction_data.push(is_buy as u8);

        // 构建账户信息
        let account_infos = vec![
            global.clone(),
            fee_recipient.clone(),
            mint.clone(),
            bonding_curve.clone(),
            associated_bonding_curve.clone(),
            associated_user.clone(),
            user.clone(),
            token_program.clone(),
        ];

        // 构建指令
        let instruction = Instruction {
            program_id: pumpswap_program_id,
            accounts: account_infos.iter().enumerate().map(|(i, account)| {
                AccountMeta {
                    pubkey: *account.key,
                    is_signer: account.is_signer,
                    is_writable: match i {
                        1 | 2 | 3 | 4 | 5 => true, // 需要修改的账户
                        _ => false,
                    },
                }
            }).collect(),
            data: instruction_data,
        };

        // 执行CPI调用
        let result = anchor_lang::solana_program::program::invoke(&instruction, &account_infos);

        match result {
            Ok(_) => {
                msg!("PumpSwap交换成功: {} -> {} ({})", 
                     amount_in, min_amount_out, if is_buy { "BUY" } else { "SELL" });
                Ok(min_amount_out)
            },
            Err(e) => {
                msg!("PumpSwap交换失败: {:?}", e);
                Err(RouteError::DexCpiCallFailed.into())
            }
        }
    }

    fn get_dex_type(&self) -> Dex {
        Dex::PumpSwap
    }

    fn get_swap_fee_bps(&self) -> u16 {
        // PumpSwap 的费用通常为 1%
        100
    }

    fn validate_accounts(&self, accounts: &[AccountInfo]) -> Result<()> {
        require!(
            accounts.len() >= 8,
            RouteError::InvalidDexAccounts
        );

        // 验证关键账户
        let mint = &accounts[2];
        let bonding_curve = &accounts[3];
        let associated_user = &accounts[5];
        let user = &accounts[6];

        require!(
            mint.is_writable && bonding_curve.is_writable && associated_user.is_writable,
            RouteError::InvalidDexAccounts
        );

        require!(
            user.is_signer,
            RouteError::InvalidSwapAuthority
        );

        Ok(())
    }

    fn calculate_amount_out(&self, amount_in: u64, additional_data: &[u8]) -> Result<u64> {
        // PumpSwap使用bonding curve模型
        // 简化的bonding curve计算
        
        if additional_data.len() < 24 {
            // 如果没有足够的curve数据，使用线性估算
            return Ok(amount_in * 95 / 100); // 假设5%的价格影响
        }

        // 解析bonding curve参数（简化版）
        // 假设数据格式：[virtual_sol_reserves: 8 bytes, virtual_token_reserves: 8 bytes, real_sol_reserves: 8 bytes]
        let virtual_sol_reserves = u64::from_le_bytes([
            additional_data[0], additional_data[1], additional_data[2], additional_data[3],
            additional_data[4], additional_data[5], additional_data[6], additional_data[7],
        ]);
        
        let virtual_token_reserves = u64::from_le_bytes([
            additional_data[8], additional_data[9], additional_data[10], additional_data[11],
            additional_data[12], additional_data[13], additional_data[14], additional_data[15],
        ]);

        // 使用bonding curve公式计算输出
        let amount_out = self.calculate_bonding_curve_out(
            amount_in,
            virtual_sol_reserves,
            virtual_token_reserves,
        )?;

        Ok(amount_out)
    }
}

impl PumpSwapProcessor {
    /// 计算bonding curve输出金额
    /// 使用 x * y = k 恒定乘积公式的变形
    fn calculate_bonding_curve_out(
        &self,
        amount_in: u64,
        virtual_sol_reserves: u64,
        virtual_token_reserves: u64,
    ) -> Result<u64> {
        if virtual_sol_reserves == 0 || virtual_token_reserves == 0 {
            return Err(RouteError::InsufficientLiquidity.into());
        }

        // 扣除费用
        let fee_bps = self.get_swap_fee_bps();
        let amount_in_after_fee = amount_in
            .checked_mul(10000u64.saturating_sub(fee_bps as u64))
            .and_then(|x| x.checked_div(10000))
            .ok_or(RouteError::MathOverflow)?;

        // Bonding curve计算: amount_out = (amount_in * virtual_token_reserves) / (virtual_sol_reserves + amount_in)
        let numerator = amount_in_after_fee
            .checked_mul(virtual_token_reserves)
            .ok_or(RouteError::MathOverflow)?;

        let denominator = virtual_sol_reserves
            .checked_add(amount_in_after_fee)
            .ok_or(RouteError::MathOverflow)?;

        let amount_out = numerator
            .checked_div(denominator)
            .ok_or(RouteError::DivisionByZero)?;

        Ok(amount_out)
    }

    /// 计算买入价格
    pub fn calculate_buy_price(
        &self,
        sol_amount: u64,
        virtual_sol_reserves: u64,
        virtual_token_reserves: u64,
    ) -> Result<u64> {
        self.calculate_bonding_curve_out(sol_amount, virtual_sol_reserves, virtual_token_reserves)
    }

    /// 计算卖出价格
    pub fn calculate_sell_price(
        &self,
        token_amount: u64,
        virtual_sol_reserves: u64,
        virtual_token_reserves: u64,
    ) -> Result<u64> {
        // 卖出时，token作为输入，SOL作为输出
        self.calculate_bonding_curve_out(token_amount, virtual_token_reserves, virtual_sol_reserves)
    }
}

/// 根据DEX类型创建相应的处理器
pub fn create_pumpswap_processor() -> Box<dyn DexProcessor> {
    Box::new(PumpSwapProcessor)
}

/// PumpSwap 交换数据结构
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct PumpSwapData {
    pub amount: u64,
    pub min_sol_output: u64,
    pub is_buy: bool,
}

/// PumpSwap Bonding Curve 状态
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct PumpSwapBondingCurve {
    pub virtual_token_reserves: u64,
    pub virtual_sol_reserves: u64,
    pub real_token_reserves: u64,
    pub real_sol_reserves: u64,
    pub token_total_supply: u64,
    pub complete: bool,
}

/// PumpSwap 全局配置
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct PumpSwapGlobal {
    pub initialized: bool,
    pub authority: Pubkey,
    pub fee_recipient: Pubkey,
    pub initial_virtual_token_reserves: u64,
    pub initial_virtual_sol_reserves: u64,
    pub initial_real_token_reserves: u64,
    pub token_total_supply: u64,
    pub fee_basis_points: u64,
}

/// 计算PumpSwap市场价格
pub fn calculate_market_price(
    virtual_sol_reserves: u64,
    virtual_token_reserves: u64,
) -> Result<u64> {
    if virtual_token_reserves == 0 {
        return Err(RouteError::DivisionByZero.into());
    }

    // 价格 = virtual_sol_reserves / virtual_token_reserves
    // 返回每个token的SOL价格（乘以精度因子）
    let price = virtual_sol_reserves
        .checked_mul(1_000_000) // 精度因子
        .and_then(|x| x.checked_div(virtual_token_reserves))
        .ok_or(RouteError::MathOverflow)?;

    Ok(price)
}

/// 计算价格影响
pub fn calculate_pump_price_impact(
    amount_in: u64,
    virtual_sol_reserves: u64,
    virtual_token_reserves: u64,
) -> Result<u16> {
    if virtual_sol_reserves == 0 {
        return Ok(10000); // 100% 价格影响
    }

    // 价格影响 = amount_in / virtual_sol_reserves * 10000
    let price_impact = amount_in
        .checked_mul(10000)
        .and_then(|x| x.checked_div(virtual_sol_reserves))
        .ok_or(RouteError::MathOverflow)?;

    Ok(price_impact.min(10000) as u16)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_pumpswap_processor() {
        let processor = PumpSwapProcessor;
        assert_eq!(processor.get_dex_type(), Dex::PumpSwap);
        assert_eq!(processor.get_swap_fee_bps(), 100);
    }

    #[test]
    fn test_bonding_curve_calculation() {
        let processor = PumpSwapProcessor;
        
        let amount_out = processor.calculate_bonding_curve_out(
            1_000_000, // 1 SOL
            10_000_000_000, // 10k SOL virtual reserves
            1_000_000_000_000, // 1M token virtual reserves
        ).unwrap();
        
        assert!(amount_out > 0);
        assert!(amount_out < 1_000_000_000_000); // Should be less than total reserves
    }

    #[test]
    fn test_price_calculations() {
        let virtual_sol = 1_000_000_000; // 1k SOL
        let virtual_token = 1_000_000_000_000; // 1M tokens
        
        let market_price = calculate_market_price(virtual_sol, virtual_token).unwrap();
        assert!(market_price > 0);
        
        let price_impact = calculate_pump_price_impact(100_000, virtual_sol, virtual_token).unwrap();
        assert!(price_impact < 10000); // Should be less than 100%
    }

    #[test]
    fn test_buy_sell_price() {
        let processor = PumpSwapProcessor;
        let virtual_sol = 1_000_000_000;
        let virtual_token = 1_000_000_000_000;
        
        let buy_price = processor.calculate_buy_price(
            1_000_000, // 1 SOL
            virtual_sol,
            virtual_token,
        ).unwrap();
        assert!(buy_price > 0);
        
        let sell_price = processor.calculate_sell_price(
            buy_price, // Use the tokens we just "bought"
            virtual_sol,
            virtual_token,
        ).unwrap();
        
        // Should get back less SOL due to fees and price impact
        assert!(sell_price < 1_000_000);
    }
}