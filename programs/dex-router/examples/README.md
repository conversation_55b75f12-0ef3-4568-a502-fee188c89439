# DEX Router 指令调用示例

本目录包含了 DEX Router 程序各种指令的调用示例，展示如何在实际应用中使用这些指令。

## 目录结构

```
examples/
├── README.md                      # 本文件
├── initialize_config.rs           # Rust 版本的配置初始化示例
├── initialize_user_position.rs    # Rust 版本的用户位置初始化示例
├── initialize_config.ts           # TypeScript 版本的配置初始化示例
└── initialize_user_position.ts    # TypeScript 版本的用户位置初始化示例
```

## 可用指令

### 1. initialize_config
初始化 DEX Router 的全局配置，设置支持的 DEX、费率参数、限制等。

**权限要求**: 管理员账户
**账户创建**: 创建全局配置 PDA 账户

### 2. initialize_user_position  
为用户初始化位置跟踪账户，用于跟踪交易历史、风险评分等。

**权限要求**: 用户账户
**账户创建**: 创建用户特定的位置 PDA 账户

## 使用方法

### Rust 示例

#### 前置要求
```bash
# 添加依赖到 Cargo.toml
[dependencies]
anchor-client = "0.29.0"
anchor-lang = "0.29.0"
solana-sdk = "1.16.0"
dex-router = { path = "../programs/dex-router" }
```

#### 运行示例
```bash
# 配置初始化示例
cargo run --example initialize_config

# 用户位置初始化示例  
cargo run --example initialize_user_position
```

#### 测试
```bash
# 运行测试
cargo test --example initialize_config
cargo test --example initialize_user_position
```

### TypeScript 示例

#### 前置要求
```bash
# 安装依赖
npm install @coral-xyz/anchor @solana/web3.js

# 确保已构建程序并生成 IDL
anchor build
```

#### 配置环境
```bash
# 设置 Solana 配置
solana config set --url devnet  # 或 mainnet-beta
solana config set --keypair ~/.config/solana/id.json

# 设置 Anchor 环境变量
export ANCHOR_PROVIDER_URL="https://api.devnet.solana.com"
export ANCHOR_WALLET="~/.config/solana/id.json"
```

#### 运行示例
```bash
# 配置初始化示例
npx ts-node examples/initialize_config.ts

# 用户位置初始化示例
npx ts-node examples/initialize_user_position.ts
```

## 配置参数说明

### ConfigArgs 结构
```rust
pub struct ConfigArgs {
    pub max_route_amount: u64,        // 最大单次路由金额
    pub max_flash_loan_amount: u64,   // 最大闪电贷金额  
    pub max_slippage_bps: u16,        // 最大允许滑点(基点)
    pub min_profit_threshold: u64,    // 最小利润阈值
    pub protocol_fee_bps: u16,        // 协议费率(基点)
    pub max_route_steps: u8,          // 最大路由步骤数
}
```

### 默认值
- `max_route_amount`: 1,000,000,000,000 (1M USDC)
- `max_flash_loan_amount`: 10,000,000,000,000 (10M USDC)  
- `max_slippage_bps`: 300 (3%)
- `min_profit_threshold`: 1,000,000 (1 USDC)
- `protocol_fee_bps`: 30 (0.3%)
- `max_route_steps`: 6

## PDA 账户地址

### 配置账户 PDA
```
seeds: ["config"]
program_id: 9zYWuMhEVUj953Zv3Aq2VvBGHrxsbfgnFbWDLjbTocPo
```

### 用户位置账户 PDA  
```
seeds: ["position", user_pubkey]
program_id: 9zYWuMhEVUj953Zv3Aq2VvBGHrxsbfgnFbWDLjbTocPo
```

## 支持的 DEX

默认启用的 DEX 包括:
- Raydium CLMM (ID: 0)
- Raydium CPMM (ID: 1) 
- Meteora DLMM (ID: 2)
- Meteora AMM (ID: 3)
- Orca Whirlpool (ID: 4)
- PumpSwap (ID: 5)

## 错误处理

常见错误及解决方案:

### 1. 账户已存在
```
Error: Account already exists
```
**解决方案**: 检查 PDA 账户是否已经初始化

### 2. 权限不足
```
Error: Insufficient permissions
```
**解决方案**: 确保使用正确的签名者账户

### 3. 余额不足
```
Error: Insufficient funds
```
**解决方案**: 确保账户有足够的 SOL 支付交易费用和账户租金

## 最佳实践

1. **测试环境**: 先在 devnet 上测试所有功能
2. **错误处理**: 实现完整的错误处理和重试机制
3. **账户验证**: 在调用指令前检查账户状态
4. **费用估算**: 预估交易费用和账户租金
5. **批量操作**: 对于多个操作，考虑使用批量处理以节省费用

## 进阶用法

### 自定义配置参数
```typescript
const customConfig = {
  maxRouteAmount: new anchor.BN(500_000_000_000), // 500K USDC
  maxSlippageBps: 200, // 2%
  protocolFeeBps: 50,  // 0.5%
  // ... 其他参数
};
```

### 批量用户初始化
```typescript
const users = [keypair1, keypair2, keypair3];
await batchInitializeUserPositions(users);
```

## 支持

如有问题或建议，请提交 Issue 或联系开发团队。
