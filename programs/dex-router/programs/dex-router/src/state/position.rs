//! 用户位置和状态管理
//!
//! 跟踪用户的交易历史、风险评级和状态

use anchor_lang::prelude::*;

/// 用户位置状态
/// 记录用户的交易统计和风险管理信息
#[account]
#[derive(Debug)]
pub struct UserPosition {
    /// 用户公钥
    pub owner: Pubkey,

    /// 总交易量（以USDC计价）
    pub total_volume: u64,

    /// 今日交易量
    pub daily_volume: u64,

    /// 今日交易量重置时间戳
    pub daily_volume_reset_at: i64,

    /// 成功交易次数
    pub successful_trades: u32,

    /// 失败交易次数
    pub failed_trades: u32,

    /// 总利润（以USDC计价）
    pub total_profit: i64,

    /// 最大单次损失
    pub max_loss: u64,

    /// 当前风险评分（0-10）
    pub risk_score: u8,

    /// 用户等级（0-5）
    pub user_level: u8,

    /// 是否被暂停
    pub is_suspended: bool,

    /// 暂停原因
    pub suspension_reason: u8,

    /// 暂停时间戳
    pub suspended_at: i64,

    /// 最后活动时间戳
    pub last_activity_at: i64,

    /// 首次交易时间戳
    pub first_trade_at: i64,

    /// 偏好的DEX列表（位标志）
    pub preferred_dex_flags: u64,

    /// 最大允许滑点偏好（基点）
    pub preferred_max_slippage_bps: u16,

    /// 风险偏好等级（0-5，0最保守）
    pub risk_preference: u8,

    /// 保留字段用于未来扩展
    pub reserved: [u64; 6],
}

impl UserPosition {
    /// 账户空间大小
    pub const LEN: usize = 8 + // discriminator
        32 + // owner
        8 +  // total_volume
        8 +  // daily_volume
        8 +  // daily_volume_reset_at
        4 +  // successful_trades
        4 +  // failed_trades
        8 +  // total_profit
        8 +  // max_loss
        1 +  // risk_score
        1 +  // user_level
        1 +  // is_suspended
        1 +  // suspension_reason
        8 +  // suspended_at
        8 +  // last_activity_at
        8 +  // first_trade_at
        8 +  // preferred_dex_flags
        2 +  // preferred_max_slippage_bps
        1 +  // risk_preference
        48;  // reserved

    /// 创建新用户位置
    pub fn new(owner: Pubkey, current_timestamp: i64) -> Self {
        Self {
            owner,
            total_volume: 0,
            daily_volume: 0,
            daily_volume_reset_at: current_timestamp,
            successful_trades: 0,
            failed_trades: 0,
            total_profit: 0,
            max_loss: 0,
            risk_score: 5, // 中等风险
            user_level: 0, // 新手
            is_suspended: false,
            suspension_reason: 0,
            suspended_at: 0,
            last_activity_at: current_timestamp,
            first_trade_at: current_timestamp,
            preferred_dex_flags: 0xFFFFFFFFFFFFFFFF, // 默认所有DEX
            preferred_max_slippage_bps: 300, // 3%
            risk_preference: 2, // 中等风险偏好
            reserved: [0; 6],
        }
    }

    /// 更新日交易量
    pub fn update_daily_volume(&mut self, amount: u64, current_timestamp: i64) -> Result<()> {
        // 检查是否需要重置日交易量
        let day_in_seconds = 24 * 60 * 60;
        if current_timestamp - self.daily_volume_reset_at >= day_in_seconds {
            self.daily_volume = 0;
            self.daily_volume_reset_at = current_timestamp;
        }

        self.daily_volume = self.daily_volume.checked_add(amount)
            .ok_or(crate::error::RouteError::MathOverflow)?;
        
        self.total_volume = self.total_volume.checked_add(amount)
            .ok_or(crate::error::RouteError::MathOverflow)?;

        self.last_activity_at = current_timestamp;
        Ok(())
    }

    /// 记录成功交易
    pub fn record_successful_trade(&mut self, profit: i64) -> Result<()> {
        self.successful_trades = self.successful_trades.checked_add(1)
            .ok_or(crate::error::RouteError::MathOverflow)?;
        
        self.total_profit = self.total_profit.checked_add(profit)
            .ok_or(crate::error::RouteError::MathOverflow)?;

        // 更新用户等级
        self.update_user_level();
        
        // 更新风险评分（成功交易降低风险）
        if self.risk_score > 1 {
            self.risk_score = self.risk_score.saturating_sub(1);
        }

        Ok(())
    }

    /// 记录失败交易
    pub fn record_failed_trade(&mut self, loss: u64) -> Result<()> {
        self.failed_trades = self.failed_trades.checked_add(1)
            .ok_or(crate::error::RouteError::MathOverflow)?;
        
        if loss > self.max_loss {
            self.max_loss = loss;
        }

        // 更新风险评分（失败交易增加风险）
        if self.risk_score < 10 {
            self.risk_score = self.risk_score.saturating_add(1);
        }

        Ok(())
    }

    /// 更新用户等级
    fn update_user_level(&mut self) {
        let total_trades = self.successful_trades + self.failed_trades;
        
        self.user_level = match total_trades {
            0..=10 => 0,      // 新手
            11..=50 => 1,     // 初级
            51..=200 => 2,    // 中级
            201..=1000 => 3,  // 高级
            1001..=5000 => 4, // 专家
            _ => 5,           // 大师
        };
    }

    /// 计算成功率
    pub fn success_rate(&self) -> f64 {
        let total = self.successful_trades + self.failed_trades;
        if total == 0 {
            0.0
        } else {
            (self.successful_trades as f64) / (total as f64)
        }
    }

    /// 检查是否可以执行交易
    pub fn can_trade(&self, config: &super::config::RouterConfig) -> Result<()> {
        // 检查暂停状态
        require!(!self.is_suspended, crate::error::RouteError::UserSuspended);

        // 检查日交易量限制
        require!(
            self.daily_volume <= config.max_daily_volume_per_user,
            crate::error::RouteError::RateLimitExceeded
        );

        Ok(())
    }

    /// 暂停用户
    pub fn suspend(&mut self, reason: u8, timestamp: i64) {
        self.is_suspended = true;
        self.suspension_reason = reason;
        self.suspended_at = timestamp;
    }

    /// 恢复用户
    pub fn resume(&mut self) {
        self.is_suspended = false;
        self.suspension_reason = 0;
        self.suspended_at = 0;
    }

    /// 检查用户风险等级
    pub fn get_risk_level(&self) -> RiskLevel {
        match self.risk_score {
            0..=2 => RiskLevel::Low,
            3..=5 => RiskLevel::Medium,
            6..=7 => RiskLevel::High,
            8..=10 => RiskLevel::Critical,
            _ => RiskLevel::Critical,
        }
    }
}

/// 风险等级枚举
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}

/// 暂停原因枚举
pub mod suspension_reasons {
    pub const RISK_TOO_HIGH: u8 = 1;
    pub const SUSPICIOUS_ACTIVITY: u8 = 2;
    pub const RATE_LIMIT_EXCEEDED: u8 = 3;
    pub const ADMIN_SUSPENSION: u8 = 4;
    pub const SECURITY_VIOLATION: u8 = 5;
}