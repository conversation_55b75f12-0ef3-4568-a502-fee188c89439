//! DEX Router 配置初始化示例
//! 
//! 此示例展示如何初始化 DEX Router 的全局配置
//! 包括设置支持的 DEX、费率参数、限制等

use anchor_client::solana_sdk::{
    commitment_config::CommitmentConfig,
    pubkey::Pubkey,
    signature::{Keypair, Signer},
    system_program,
};
use anchor_client::{Client, Cluster};
use dex_router::{
    constants::{seeds, dex_ids, fee_rates, amount_limits},
    state::ConfigArgs,
    ID as PROGRAM_ID,
};
use std::rc::Rc;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 1. 设置客户端连接
    let payer = Keypair::new(); // 在实际使用中，应该从文件或环境变量加载
    let client = Client::new_with_options(
        Cluster::Devnet, // 或者使用 Cluster::Mainnet
        Rc::new(payer),
        CommitmentConfig::processed(),
    );
    let program = client.program(PROGRAM_ID)?;

    // 2. 生成配置账户的 PDA
    let (config_pda, config_bump) = Pubkey::find_program_address(
        &[seeds::CONFIG],
        &PROGRAM_ID,
    );

    println!("配置账户 PDA: {}", config_pda);
    println!("配置账户 Bump: {}", config_bump);

    // 3. 准备配置参数
    let config_args = ConfigArgs {
        max_route_amount: amount_limits::DEFAULT_MAX_ROUTE_AMOUNT, // 1M USDC
        max_flash_loan_amount: amount_limits::DEFAULT_MAX_FLASH_LOAN_AMOUNT, // 10M USDC
        max_slippage_bps: fee_rates::DEFAULT_MAX_SLIPPAGE_BPS, // 3%
        min_profit_threshold: amount_limits::MIN_PROFIT_THRESHOLD, // 1 USDC
        protocol_fee_bps: fee_rates::DEFAULT_PROTOCOL_FEE_BPS, // 0.3%
        max_route_steps: 6, // 最大路由步骤数
    };

    println!("配置参数:");
    println!("  最大路由金额: {} (约 {} USDC)", config_args.max_route_amount, config_args.max_route_amount / 1_000_000);
    println!("  最大闪电贷金额: {} (约 {} USDC)", config_args.max_flash_loan_amount, config_args.max_flash_loan_amount / 1_000_000);
    println!("  最大滑点: {} 基点 ({}%)", config_args.max_slippage_bps, config_args.max_slippage_bps as f64 / 100.0);
    println!("  最小利润阈值: {} (约 {} USDC)", config_args.min_profit_threshold, config_args.min_profit_threshold / 1_000_000);
    println!("  协议费率: {} 基点 ({}%)", config_args.protocol_fee_bps, config_args.protocol_fee_bps as f64 / 100.0);
    println!("  最大路由步骤: {}", config_args.max_route_steps);

    // 4. 构建并发送交易
    let tx = program
        .request()
        .accounts(dex_router::accounts::InitializeConfig {
            admin: program.payer(),
            config: config_pda,
            system_program: system_program::ID,
        })
        .args(dex_router::instruction::InitializeConfig {
            config_data: config_args,
        })
        .send()?;

    println!("交易签名: {}", tx);
    println!("配置初始化成功！");

    // 5. 验证配置账户
    let config_account: dex_router::state::RouterConfig = program.account(config_pda)?;
    println!("\n已创建的配置:");
    println!("  管理员: {}", config_account.admin);
    println!("  支持的 DEX 标志: 0b{:064b}", config_account.supported_dex_flags);
    println!("  创建时间: {}", config_account.created_at);
    
    // 检查默认启用的 DEX
    println!("\n默认启用的 DEX:");
    if config_account.is_dex_supported(dex_ids::RAYDIUM_CLMM) {
        println!("  ✓ Raydium CLMM");
    }
    if config_account.is_dex_supported(dex_ids::RAYDIUM_CPMM) {
        println!("  ✓ Raydium CPMM");
    }
    if config_account.is_dex_supported(dex_ids::METEORA_DLMM) {
        println!("  ✓ Meteora DLMM");
    }
    if config_account.is_dex_supported(dex_ids::METEORA_AMM) {
        println!("  ✓ Meteora AMM");
    }
    if config_account.is_dex_supported(dex_ids::ORCA_WHIRLPOOL) {
        println!("  ✓ Orca Whirlpool");
    }
    if config_account.is_dex_supported(dex_ids::PUMPSWAP) {
        println!("  ✓ PumpSwap");
    }

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use anchor_client::solana_sdk::signature::Keypair;

    #[test]
    fn test_config_pda_generation() {
        let (config_pda, _bump) = Pubkey::find_program_address(
            &[seeds::CONFIG],
            &PROGRAM_ID,
        );
        
        // 验证 PDA 生成是确定性的
        let (config_pda2, _bump2) = Pubkey::find_program_address(
            &[seeds::CONFIG],
            &PROGRAM_ID,
        );
        
        assert_eq!(config_pda, config_pda2);
    }

    #[test]
    fn test_config_args_default() {
        let config_args = ConfigArgs::default();
        
        assert_eq!(config_args.max_route_amount, amount_limits::DEFAULT_MAX_ROUTE_AMOUNT);
        assert_eq!(config_args.max_flash_loan_amount, amount_limits::DEFAULT_MAX_FLASH_LOAN_AMOUNT);
        assert_eq!(config_args.max_slippage_bps, fee_rates::DEFAULT_MAX_SLIPPAGE_BPS);
        assert_eq!(config_args.min_profit_threshold, amount_limits::MIN_PROFIT_THRESHOLD);
        assert_eq!(config_args.protocol_fee_bps, fee_rates::DEFAULT_PROTOCOL_FEE_BPS);
        assert_eq!(config_args.max_route_steps, 6);
    }
}
