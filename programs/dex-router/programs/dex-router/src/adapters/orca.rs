//! Orca DEX 适配器
//!
//! 支持Orca的Whirlpool协议

use anchor_lang::prelude::*;
use anchor_spl::token::TokenAccount;
use anchor_lang::solana_program::{instruction::Instruction, instruction::AccountMeta};
use std::str::FromStr;
use crate::adapters::common::*;
use crate::constants::Dex;
use crate::error::RouteError;

/// Orca Whirlpool 适配器
pub struct OrcaProcessor;

impl DexProcessor for OrcaProcessor {
    fn execute_swap_cpi<'info>(
        &self,
        accounts: &[AccountInfo<'info>],
        amount_in: u64,
        min_amount_out: u64,
        additional_args: &[u8],
    ) -> Result<u64> {
        // 验证账户数量
        require!(
            accounts.len() >= 9,
            RouteError::InvalidDexAccounts
        );

        // Orca Whirlpool 账户布局
        let whirlpool = &accounts[0];
        let token_authority = &accounts[1];
        let token_a = &accounts[2];
        let token_b = &accounts[3];
        let token_vault_a = &accounts[4];
        let token_vault_b = &accounts[5];
        let tick_array_0 = &accounts[6];
        let tick_array_1 = &accounts[7];
        let tick_array_2 = &accounts[8];

        // Orca Whirlpool 程序ID
        let orca_whirlpool_program_id = Pubkey::from_str("whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc")
            .map_err(|_| RouteError::SystemConfigError)?;

        // 构建交换指令数据
        let mut instruction_data = Vec::new();
        
        // Orca Whirlpool swap 指令标识符
        let discriminator: [u8; 8] = [0xf8, 0xc6, 0x9e, 0x91, 0xe1, 0x7a, 0x99, 0xc8];
        instruction_data.extend_from_slice(&discriminator);
        
        // 添加交换参数
        instruction_data.extend_from_slice(&amount_in.to_le_bytes());
        instruction_data.extend_from_slice(&min_amount_out.to_le_bytes());
        
        // 添加sqrt_price_limit (如果在additional_args中提供)
        if additional_args.len() >= 16 {
            instruction_data.extend_from_slice(&additional_args[0..16]); // u128 sqrt_price_limit
        } else {
            // 使用默认值 (无价格限制)
            instruction_data.extend_from_slice(&[0u8; 16]);
        }
        
        // 添加amount_specified_is_input flag
        instruction_data.push(1u8); // true for exact input
        
        // 添加a_to_b flag (从additional_args中获取或默认)
        let a_to_b = if additional_args.len() > 16 {
            additional_args[16] != 0
        } else {
            true // 默认从A到B
        };
        instruction_data.push(a_to_b as u8);

        // 构建账户信息
        let account_infos = vec![
            whirlpool.clone(),
            token_authority.clone(),
            token_a.clone(),
            token_b.clone(),
            token_vault_a.clone(),
            token_vault_b.clone(),
            tick_array_0.clone(),
            tick_array_1.clone(),
            tick_array_2.clone(),
        ];

        // 构建指令
        let instruction = Instruction {
            program_id: orca_whirlpool_program_id,
            accounts: account_infos.iter().enumerate().map(|(i, account)| {
                AccountMeta {
                    pubkey: *account.key,
                    is_signer: account.is_signer,
                    is_writable: match i {
                        0 | 2 | 3 | 4 | 5 | 6 | 7 | 8 => true, // 需要修改的账户
                        _ => false,
                    },
                }
            }).collect(),
            data: instruction_data,
        };

        // 执行CPI调用
        let result = anchor_lang::solana_program::program::invoke(&instruction, &account_infos);

        match result {
            Ok(_) => {
                msg!("Orca Whirlpool交换成功: {} -> {}", amount_in, min_amount_out);
                Ok(min_amount_out)
            },
            Err(e) => {
                msg!("Orca Whirlpool交换失败: {:?}", e);
                Err(RouteError::DexCpiCallFailed.into())
            }
        }
    }

    fn get_dex_type(&self) -> Dex {
        Dex::Orca
    }

    fn get_swap_fee_bps(&self) -> u16 {
        // Orca Whirlpool 的费用通常为 0.3%，但可能根据池子不同而变化
        30
    }

    fn validate_accounts(&self, accounts: &[AccountInfo]) -> Result<()> {
        require!(
            accounts.len() >= 9,
            RouteError::InvalidDexAccounts
        );

        // 验证关键账户
        let whirlpool = &accounts[0];
        let token_a = &accounts[2];
        let token_b = &accounts[3];

        require!(
            whirlpool.is_writable && token_a.is_writable && token_b.is_writable,
            RouteError::InvalidDexAccounts
        );

        // 验证tick arrays
        for i in 6..9 {
            if i < accounts.len() {
                require!(
                    accounts[i].is_writable,
                    RouteError::InvalidDexAccounts
                );
            }
        }

        Ok(())
    }

    fn calculate_amount_out(&self, amount_in: u64, additional_data: &[u8]) -> Result<u64> {
        // Orca使用CLMM模型，需要更复杂的计算
        // 这里提供简化版本，实际应用中需要使用精确的数学公式
        
        if additional_data.len() < 32 {
            // 如果没有足够的池数据，使用默认估算
            return Ok(amount_in); // 1:1 估算
        }

        // 从additional_data解析池状态（简化版）
        // 实际实现需要根据Whirlpool的状态结构来解析
        
        // 假设数据格式：[sqrt_price_x64: 16 bytes, liquidity: 16 bytes]
        let sqrt_price_bytes = &additional_data[0..16];
        let liquidity_bytes = &additional_data[16..32];
        
        // 这里应该实现精确的CLMM计算
        // 为简化起见，返回基于简单比例的估算
        Ok(amount_in * 99 / 100) // 假设1%的价格影响
    }
}

/// 根据DEX类型创建相应的处理器
pub fn create_orca_processor() -> Box<dyn DexProcessor> {
    Box::new(OrcaProcessor)
}

/// Orca Whirlpool 交换数据结构
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct OrcaSwapData {
    pub amount: u64,
    pub other_amount_threshold: u64,
    pub sqrt_price_limit: u128,
    pub amount_specified_is_input: bool,
    pub a_to_b: bool,
}

/// Orca Whirlpool 池状态（简化版）
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct OrcaWhirlpoolState {
    pub whirlpool_config: Pubkey,
    pub whirlpool_config_extension: Pubkey,
    pub token_mint_a: Pubkey,
    pub token_mint_b: Pubkey,
    pub token_vault_a: Pubkey,
    pub token_vault_b: Pubkey,
    pub fee_rate: u16,
    pub protocol_fee_rate: u16,
    pub liquidity: u128,
    pub sqrt_price: u128,
    pub tick_current_index: i32,
    pub protocol_fee_owed_a: u64,
    pub protocol_fee_owed_b: u64,
    pub fee_growth_global_a: u128,
    pub fee_growth_global_b: u128,
    pub reward_last_updated_timestamp: u64,
}

/// Orca Whirlpool 配置
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct OrcaWhirlpoolConfig {
    pub fee_authority: Pubkey,
    pub collect_protocol_fees_authority: Pubkey,
    pub reward_emissions_authority: Pubkey,
    pub default_protocol_fee_rate: u16,
}

/// Tick Array 状态（简化版）
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct OrcaTickArrayState {
    pub whirlpool: Pubkey,
    pub start_tick_index: i32,
}

/// 辅助函数：计算Orca交换的价格影响
pub fn calculate_orca_price_impact(
    amount_in: u64,
    sqrt_price: u128,
    liquidity: u128,
    fee_rate: u16,
) -> Result<u16> {
    // 简化的价格影响计算
    // 实际实现需要使用Orca的精确数学公式
    
    if liquidity == 0 {
        return Ok(10000); // 100% 价格影响
    }
    
    // 基于流动性的简单价格影响估算
    let impact_factor = (amount_in as u128 * 10000) / liquidity.max(1);
    let price_impact = impact_factor.min(10000) as u16;
    
    // 添加费用影响
    let total_impact = price_impact.saturating_add(fee_rate);
    
    Ok(total_impact.min(10000))
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_orca_processor() {
        let processor = OrcaProcessor;
        assert_eq!(processor.get_dex_type(), Dex::Orca);
        assert_eq!(processor.get_swap_fee_bps(), 30);
    }

    #[test]
    fn test_price_impact_calculation() {
        let impact = calculate_orca_price_impact(
            1000000, // 1 USDC
            1000000000000000000u128, // sqrt_price
            100000000000000u128, // liquidity
            30, // fee_rate (0.3%)
        ).unwrap();
        
        assert!(impact > 0);
        assert!(impact <= 10000);
    }

    #[test]
    fn test_calculate_amount_out() {
        let processor = OrcaProcessor;
        let additional_data = vec![0u8; 32]; // Mock pool data
        
        let amount_out = processor.calculate_amount_out(1000000, &additional_data).unwrap();
        assert_eq!(amount_out, 990000); // 99% of input (1% price impact)
    }
}