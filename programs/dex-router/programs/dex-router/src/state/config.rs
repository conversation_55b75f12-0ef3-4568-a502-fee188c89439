//! 路由器全局配置状态
//!
//! 管理系统级配置参数和控制状态

use anchor_lang::prelude::*;

/// 路由器全局配置
/// 存储系统级参数和控制开关
#[account]
#[derive(Debug)]
pub struct RouterConfig {
    /// 管理员公钥（有权修改配置）
    pub admin: Pubkey,

    /// 支持的DEX列表（使用位标志）
    pub supported_dex_flags: u64,

    /// 最大单次路由金额
    pub max_route_amount: u64,

    /// 最大闪电贷金额
    pub max_flash_loan_amount: u64,

    /// 最大允许滑点（基点，如300表示3%）
    pub max_slippage_bps: u16,

    /// 最小利润阈值
    pub min_profit_threshold: u64,

    /// 最大Gas费用
    pub max_gas_fee: u64,

    /// 协议费率（基点）
    pub protocol_fee_bps: u16,

    /// 最小路由金额
    pub min_route_amount: u64,

    /// 最大路由步骤数
    pub max_route_steps: u8,

    /// 最大复杂度评分
    pub max_complexity_score: u8,

    /// 最大风险评分
    pub max_risk_score: u8,

    /// 用户最大日交易量
    pub max_daily_volume_per_user: u64,

    /// 全局紧急停止开关
    pub emergency_stop: bool,

    /// 单个DEX紧急停止标志（位标志）
    pub dex_emergency_stops: u64,

    /// 协议创建时间戳
    pub created_at: i64,

    /// 最后更新时间戳
    pub updated_at: i64,

    /// 保留字段用于未来扩展
    pub reserved: [u64; 8],
}

impl RouterConfig {
    /// 账户空间大小
    pub const LEN: usize = 8 + // discriminator
        32 + // admin
        8 +  // supported_dex_flags
        8 +  // max_route_amount
        8 +  // max_flash_loan_amount
        2 +  // max_slippage_bps
        8 +  // min_profit_threshold
        8 +  // max_gas_fee
        2 +  // protocol_fee_bps
        8 +  // min_route_amount
        1 +  // max_route_steps
        1 +  // max_complexity_score
        1 +  // max_risk_score
        8 +  // max_daily_volume_per_user
        1 +  // emergency_stop
        8 +  // dex_emergency_stops
        8 +  // created_at
        8 +  // updated_at
        64;  // reserved

    /// 默认配置参数
    pub fn default_params() -> Self {
        Self {
            admin: Pubkey::default(),
            supported_dex_flags: 0,
            max_route_amount: 1_000_000_000_000, // 1M USDC
            max_flash_loan_amount: 10_000_000_000_000, // 10M USDC
            max_slippage_bps: 300, // 3%
            min_profit_threshold: 1_000_000, // 1 USDC
            max_gas_fee: 10_000_000, // 0.01 SOL
            protocol_fee_bps: 30, // 0.3%
            min_route_amount: 1_000_000, // 1 USDC
            max_route_steps: 6,
            max_complexity_score: 10,
            max_risk_score: 7,
            max_daily_volume_per_user: 100_000_000_000_000, // 100M USDC
            emergency_stop: false,
            dex_emergency_stops: 0,
            created_at: 0,
            updated_at: 0,
            reserved: [0; 8],
        }
    }

    /// 检查DEX是否被支持
    pub fn is_dex_supported(&self, dex_id: u8) -> bool {
        if dex_id >= 64 { return false; }
        (self.supported_dex_flags & (1u64 << dex_id)) != 0
    }

    /// 启用DEX
    pub fn enable_dex(&mut self, dex_id: u8) {
        if dex_id < 64 {
            self.supported_dex_flags |= 1u64 << dex_id;
        }
    }

    /// 禁用DEX
    pub fn disable_dex(&mut self, dex_id: u8) {
        if dex_id < 64 {
            self.supported_dex_flags &= !(1u64 << dex_id);
        }
    }

    /// 检查DEX是否被紧急停止
    pub fn is_dex_emergency_stopped(&self, dex_id: u8) -> bool {
        if dex_id >= 64 { return false; }
        (self.dex_emergency_stops & (1u64 << dex_id)) != 0
    }

    /// 紧急停止DEX
    pub fn emergency_stop_dex(&mut self, dex_id: u8) {
        if dex_id < 64 {
            self.dex_emergency_stops |= 1u64 << dex_id;
        }
    }

    /// 恢复DEX
    pub fn resume_dex(&mut self, dex_id: u8) {
        if dex_id < 64 {
            self.dex_emergency_stops &= !(1u64 << dex_id);
        }
    }

    /// 验证路由参数
    pub fn validate_route_params(&self, amount: u64, steps: usize, slippage_bps: u16) -> Result<()> {
        require!(
            amount >= self.min_route_amount && amount <= self.max_route_amount,
            crate::error::RouteError::InvalidRouteConfig
        );
        require!(
            steps <= self.max_route_steps as usize,
            crate::error::RouteError::RoutePathTooLong
        );
        require!(
            slippage_bps <= self.max_slippage_bps,
            crate::error::RouteError::SlippageTooHigh
        );
        Ok(())
    }
}

/// 配置参数结构（用于初始化）
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct ConfigArgs {
    pub max_route_amount: u64,
    pub max_flash_loan_amount: u64,
    pub max_slippage_bps: u16,
    pub min_profit_threshold: u64,
    pub protocol_fee_bps: u16,
    pub max_route_steps: u8,
}

impl Default for ConfigArgs {
    fn default() -> Self {
        let default_config = RouterConfig::default_params();
        Self {
            max_route_amount: default_config.max_route_amount,
            max_flash_loan_amount: default_config.max_flash_loan_amount,
            max_slippage_bps: default_config.max_slippage_bps,
            min_profit_threshold: default_config.min_profit_threshold,
            protocol_fee_bps: default_config.protocol_fee_bps,
            max_route_steps: default_config.max_route_steps,
        }
    }
}