//! Meteora DEX 适配器
//!
//! 支持Meteora的DLMM和AMM协议

use anchor_lang::prelude::*;
use anchor_spl::token::TokenAccount;
use anchor_lang::solana_program::{instruction::Instruction, instruction::AccountMeta};
use std::str::FromStr;
use crate::adapters::common::*;
use crate::constants::Dex;
use crate::error::RouteError;

/// Meteora DLMM (Dynamic Liquidity Market Maker) 适配器
pub struct MeteoraLbProcessor;

impl DexProcessor for MeteoraLbProcessor {
    fn execute_swap_cpi<'info>(
        &self,
        accounts: &[AccountInfo<'info>],
        amount_in: u64,
        min_amount_out: u64,
        additional_args: &[u8],
    ) -> Result<u64> {
        // 验证账户数量
        require!(
            accounts.len() >= 12,
            RouteError::InvalidDexAccounts
        );

        // Meteora DLMM 账户布局
        let lb_pair = &accounts[0];
        let bin_array_bitmap_extension = &accounts[1];
        let reserve_x = &accounts[2];
        let reserve_y = &accounts[3];
        let user_token_x = &accounts[4];
        let user_token_y = &accounts[5];
        let bin_array_lower = &accounts[6];
        let bin_array_upper = &accounts[7];
        let oracle = &accounts[8];
        let host_fee_in = &accounts[9];
        let user = &accounts[10];
        let token_program = &accounts[11];

        // Meteora DLMM 程序ID
        let meteora_dlmm_program_id = Pubkey::from_str("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo")
            .map_err(|_| RouteError::SystemConfigError)?;

        // 构建交换指令数据
        let mut instruction_data = Vec::new();
        
        // Meteora DLMM swap 指令标识符
        let discriminator: [u8; 8] = [0xf8, 0xc6, 0x9e, 0x91, 0xe1, 0x7a, 0x99, 0xc8];
        instruction_data.extend_from_slice(&discriminator);
        
        // 添加交换参数
        instruction_data.extend_from_slice(&amount_in.to_le_bytes());
        instruction_data.extend_from_slice(&min_amount_out.to_le_bytes());

        // 构建账户信息
        let account_infos = vec![
            lb_pair.clone(),
            bin_array_bitmap_extension.clone(),
            reserve_x.clone(),
            reserve_y.clone(),
            user_token_x.clone(),
            user_token_y.clone(),
            bin_array_lower.clone(),
            bin_array_upper.clone(),
            oracle.clone(),
            host_fee_in.clone(),
            user.clone(),
            token_program.clone(),
        ];

        // 构建指令
        let instruction = Instruction {
            program_id: meteora_dlmm_program_id,
            accounts: account_infos.iter().enumerate().map(|(i, account)| {
                AccountMeta {
                    pubkey: *account.key,
                    is_signer: account.is_signer,
                    is_writable: match i {
                        0 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 => true, // 需要修改的账户
                        _ => false,
                    },
                }
            }).collect(),
            data: instruction_data,
        };

        // 执行CPI调用
        let result = anchor_lang::solana_program::program::invoke(&instruction, &account_infos);

        match result {
            Ok(_) => {
                msg!("Meteora DLMM交换成功: {} -> {}", amount_in, min_amount_out);
                Ok(min_amount_out)
            },
            Err(e) => {
                msg!("Meteora DLMM交换失败: {:?}", e);
                Err(RouteError::DexCpiCallFailed.into())
            }
        }
    }

    fn get_dex_type(&self) -> Dex {
        Dex::MeteoraLb
    }

    fn get_swap_fee_bps(&self) -> u16 {
        // Meteora DLMM 的费用通常为 0.3%
        30
    }

    fn validate_accounts(&self, accounts: &[AccountInfo]) -> Result<()> {
        require!(
            accounts.len() >= 12,
            RouteError::InvalidDexAccounts
        );

        // 验证关键账户
        let lb_pair = &accounts[0];
        let user_token_x = &accounts[4];
        let user_token_y = &accounts[5];

        require!(
            lb_pair.is_writable && user_token_x.is_writable && user_token_y.is_writable,
            RouteError::InvalidDexAccounts
        );

        Ok(())
    }
}

/// Meteora AMM 适配器
pub struct MeteoraAmmProcessor;

impl DexProcessor for MeteoraAmmProcessor {
    fn execute_swap_cpi<'info>(
        &self,
        accounts: &[AccountInfo<'info>],
        amount_in: u64,
        min_amount_out: u64,
        additional_args: &[u8],
    ) -> Result<u64> {
        // 验证账户数量
        require!(
            accounts.len() >= 9,
            RouteError::InvalidDexAccounts
        );

        // Meteora AMM 账户布局
        let pool = &accounts[0];
        let user_source_token = &accounts[1];
        let user_destination_token = &accounts[2];
        let pool_source_token = &accounts[3];
        let pool_destination_token = &accounts[4];
        let pool_lp_mint = &accounts[5];
        let pool_fee_account = &accounts[6];
        let user = &accounts[7];
        let token_program = &accounts[8];

        // Meteora AMM 程序ID
        let meteora_amm_program_id = Pubkey::from_str("24Uqj9JCLxUeoC3hGfh5W3s9FM9uCHDS2SG3LYwBpyTi")
            .map_err(|_| RouteError::SystemConfigError)?;

        // 构建交换指令数据
        let mut instruction_data = Vec::new();
        
        // Meteora AMM swap 指令标识符
        let discriminator: [u8; 8] = [0x09, 0x0e, 0x6e, 0x9b, 0x8b, 0x15, 0x4a, 0xc8];
        instruction_data.extend_from_slice(&discriminator);
        
        // 添加交换参数
        instruction_data.extend_from_slice(&amount_in.to_le_bytes());
        instruction_data.extend_from_slice(&min_amount_out.to_le_bytes());

        // 构建账户信息
        let account_infos = vec![
            pool.clone(),
            user_source_token.clone(),
            user_destination_token.clone(),
            pool_source_token.clone(),
            pool_destination_token.clone(),
            pool_lp_mint.clone(),
            pool_fee_account.clone(),
            user.clone(),
            token_program.clone(),
        ];

        // 构建指令
        let instruction = Instruction {
            program_id: meteora_amm_program_id,
            accounts: account_infos.iter().enumerate().map(|(i, account)| {
                AccountMeta {
                    pubkey: *account.key,
                    is_signer: account.is_signer,
                    is_writable: match i {
                        0 | 1 | 2 | 3 | 4 | 6 => true, // 需要修改的账户
                        _ => false,
                    },
                }
            }).collect(),
            data: instruction_data,
        };

        // 执行CPI调用
        let result = anchor_lang::solana_program::program::invoke(&instruction, &account_infos);

        match result {
            Ok(_) => {
                msg!("Meteora AMM交换成功: {} -> {}", amount_in, min_amount_out);
                Ok(min_amount_out)
            },
            Err(e) => {
                msg!("Meteora AMM交换失败: {:?}", e);
                Err(RouteError::DexCpiCallFailed.into())
            }
        }
    }

    fn get_dex_type(&self) -> Dex {
        Dex::MeteoraAmm
    }

    fn get_swap_fee_bps(&self) -> u16 {
        // Meteora AMM 的费用通常为 0.3%
        30
    }

    fn validate_accounts(&self, accounts: &[AccountInfo]) -> Result<()> {
        require!(
            accounts.len() >= 9,
            RouteError::InvalidDexAccounts
        );

        // 验证关键账户的可写性
        let pool = &accounts[0];
        let user_source_token = &accounts[1];
        let user_destination_token = &accounts[2];

        require!(
            pool.is_writable && user_source_token.is_writable && user_destination_token.is_writable,
            RouteError::InvalidDexAccounts
        );

        Ok(())
    }
}

/// 根据DEX类型创建相应的处理器
pub fn create_meteora_processor(dex_type: Dex) -> Box<dyn DexProcessor> {
    match dex_type {
        Dex::MeteoraLb => Box::new(MeteoraLbProcessor),
        Dex::MeteoraAmm => Box::new(MeteoraAmmProcessor),
        _ => panic!("Invalid Meteora DEX type"),
    }
}

/// Meteora DLMM 交换数据结构
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct MeteoraLbSwapData {
    pub amount_in: u64,
    pub minimum_amount_out: u64,
    pub swap_for_y: bool,
}

/// Meteora DLMM 配对状态
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct MeteoraLbPair {
    pub token_x_mint: Pubkey,
    pub token_y_mint: Pubkey,
    pub reserve_x: Pubkey,
    pub reserve_y: Pubkey,
    pub active_id: i32,
    pub bin_step: u16,
}

/// Meteora AMM 池状态
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct MeteoraAmmPool {
    pub token_a_mint: Pubkey,
    pub token_b_mint: Pubkey,
    pub token_a_vault: Pubkey,
    pub token_b_vault: Pubkey,
    pub lp_mint: Pubkey,
    pub fee_rate: u64,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_meteora_lb_processor() {
        let processor = MeteoraLbProcessor;
        assert_eq!(processor.get_dex_type(), Dex::MeteoraLb);
        assert_eq!(processor.get_swap_fee_bps(), 30);
    }

    #[test]
    fn test_meteora_amm_processor() {
        let processor = MeteoraAmmProcessor;
        assert_eq!(processor.get_dex_type(), Dex::MeteoraAmm);
        assert_eq!(processor.get_swap_fee_bps(), 30);
    }
}